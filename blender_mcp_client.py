#!/usr/bin/env python3
"""
Blender MCP Client
Connects to the Blender MCP server and creates the most complex scene possible
"""

import socket
import json
import time

class BlenderMCPClient:
    def __init__(self, host='localhost', port=9876):
        self.host = host
        self.port = port
        self.socket = None
    
    def connect(self):
        """Connect to the Blender MCP server"""
        try:
            self.socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.socket.connect((self.host, self.port))
            print(f"Connected to Blender MCP server at {self.host}:{self.port}")
            return True
        except Exception as e:
            print(f"Failed to connect: {e}")
            return False
    
    def send_command(self, command_type, params=None):
        """Send a command to the Blender MCP server"""
        if not self.socket:
            if not self.connect():
                return None

        command = {
            "type": command_type,
            "params": params or {}
        }

        try:
            # Send command
            message = json.dumps(command)
            print(f"Sending command: {command_type}")
            self.socket.sendall(message.encode('utf-8'))

            # Receive response with timeout
            self.socket.settimeout(30.0)  # 30 second timeout
            response_data = b''

            while True:
                try:
                    chunk = self.socket.recv(8192)
                    if not chunk:
                        break
                    response_data += chunk

                    # Try to parse complete JSON
                    try:
                        response = json.loads(response_data.decode('utf-8'))
                        print(f"Command {command_type} completed successfully")
                        return response
                    except json.JSONDecodeError:
                        continue

                except socket.timeout:
                    print(f"Timeout waiting for response to {command_type}")
                    break

        except Exception as e:
            print(f"Error sending command {command_type}: {e}")
            self.socket = None
            return None

        return None
    
    def execute_code(self, code):
        """Execute Python code in Blender"""
        return self.send_command("execute_code", {"code": code})
    
    def get_scene_info(self):
        """Get current scene information"""
        return self.send_command("get_scene_info")
    
    def disconnect(self):
        """Disconnect from the server"""
        if self.socket:
            self.socket.close()
            self.socket = None

def create_ultra_complex_scene():
    """Create the most complex Blender scene possible"""

    print("Starting ultra-complex scene creation...")
    client = BlenderMCPClient()

    if not client.connect():
        print("Could not connect to Blender MCP server. Make sure Blender is running with the MCP addon enabled.")
        return

    print("Connected to Blender MCP server successfully!")

    # Step 1: Clear scene and setup
    print("\n" + "="*50)
    print("STEP 1: CLEARING SCENE")
    print("="*50)
    clear_code = """
import bpy
import bmesh
import mathutils
from mathutils import Vector, Matrix, Euler
import random
import math

# Clear all existing objects
bpy.ops.object.select_all(action='SELECT')
bpy.ops.object.delete(use_global=False, confirm=False)

# Clear materials
for material in bpy.data.materials:
    bpy.data.materials.remove(material)

print("Scene cleared successfully")
"""
    result = client.execute_code(clear_code)
    print(f"Clear result: {result}")
    
    # Step 2: Create procedural city
    print("Step 2: Creating procedural city...")
    city_code = """
import random
import math

# Create ground plane
bpy.ops.mesh.primitive_plane_add(size=80, location=(0, 0, 0))
ground = bpy.context.active_object
ground.name = "CityGround"

# Create road material
road_material = bpy.data.materials.new(name="RoadMaterial")
road_material.use_nodes = True
road_material.node_tree.nodes.clear()

bsdf = road_material.node_tree.nodes.new(type='ShaderNodeBsdfPrincipled')
output = road_material.node_tree.nodes.new(type='ShaderNodeOutputMaterial')
road_material.node_tree.links.new(bsdf.outputs['BSDF'], output.inputs['Surface'])
bsdf.inputs['Base Color'].default_value = (0.2, 0.2, 0.2, 1.0)
bsdf.inputs['Roughness'].default_value = 0.8

# Create road network
city_size = 40
block_size = 8
roads_created = 0

for i in range(-city_size//2, city_size//2, block_size):
    # Horizontal roads
    bpy.ops.mesh.primitive_cube_add(size=1, location=(0, i, 0.05))
    road = bpy.context.active_object
    road.name = f"Road_H_{i}"
    road.scale = (city_size, 1.5, 0.1)
    road.data.materials.append(road_material)
    roads_created += 1
    
    # Vertical roads
    bpy.ops.mesh.primitive_cube_add(size=1, location=(i, 0, 0.05))
    road = bpy.context.active_object
    road.name = f"Road_V_{i}"
    road.scale = (1.5, city_size, 0.1)
    road.data.materials.append(road_material)
    roads_created += 1

print(f"Created {roads_created} road segments")
"""
    result = client.execute_code(city_code)
    print(f"City roads result: {result}")
    
    # Step 3: Create buildings with advanced materials
    print("Step 3: Creating buildings with advanced materials...")
    buildings_code = """
# Create building materials
glass_mat = bpy.data.materials.new(name="GlassMaterial")
glass_mat.use_nodes = True
glass_mat.node_tree.nodes.clear()

bsdf = glass_mat.node_tree.nodes.new(type='ShaderNodeBsdfPrincipled')
output = glass_mat.node_tree.nodes.new(type='ShaderNodeOutputMaterial')
glass_mat.node_tree.links.new(bsdf.outputs['BSDF'], output.inputs['Surface'])

bsdf.inputs['Base Color'].default_value = (0.8, 0.9, 1.0, 1.0)
bsdf.inputs['Transmission'].default_value = 0.95
bsdf.inputs['Roughness'].default_value = 0.0
bsdf.inputs['IOR'].default_value = 1.45

# Concrete material with procedural texture
concrete_mat = bpy.data.materials.new(name="ConcreteMaterial")
concrete_mat.use_nodes = True
concrete_mat.node_tree.nodes.clear()

bsdf = concrete_mat.node_tree.nodes.new(type='ShaderNodeBsdfPrincipled')
output = concrete_mat.node_tree.nodes.new(type='ShaderNodeOutputMaterial')
noise = concrete_mat.node_tree.nodes.new(type='ShaderNodeTexNoise')

concrete_mat.node_tree.links.new(bsdf.outputs['BSDF'], output.inputs['Surface'])
concrete_mat.node_tree.links.new(noise.outputs['Color'], bsdf.inputs['Base Color'])

bsdf.inputs['Roughness'].default_value = 0.9
noise.inputs['Scale'].default_value = 5.0

# Metal material
metal_mat = bpy.data.materials.new(name="MetalMaterial")
metal_mat.use_nodes = True
metal_mat.node_tree.nodes.clear()

bsdf = metal_mat.node_tree.nodes.new(type='ShaderNodeBsdfPrincipled')
output = metal_mat.node_tree.nodes.new(type='ShaderNodeOutputMaterial')
metal_mat.node_tree.links.new(bsdf.outputs['BSDF'], output.inputs['Surface'])

bsdf.inputs['Base Color'].default_value = (0.7, 0.7, 0.8, 1.0)
bsdf.inputs['Metallic'].default_value = 1.0
bsdf.inputs['Roughness'].default_value = 0.2

materials = [glass_mat, concrete_mat, metal_mat]

# Create buildings
buildings_created = 0
random.seed(42)

for block_x in range(-30, 31, 8):
    for block_y in range(-30, 31, 8):
        if buildings_created >= 50:
            break
        
        # Skip road areas
        if abs(block_x) < 2 or abs(block_y) < 2:
            continue
        
        # Create 1-2 buildings per block
        for _ in range(random.randint(1, 2)):
            if buildings_created >= 50:
                break
            
            x = block_x + random.uniform(-2, 2)
            y = block_y + random.uniform(-2, 2)
            
            # Random building dimensions
            width = random.uniform(2, 6)
            depth = random.uniform(2, 6)
            height = random.uniform(5, 25)
            
            # Create building
            bpy.ops.mesh.primitive_cube_add(size=1, location=(x, y, height/2))
            building = bpy.context.active_object
            building.name = f"Building_{buildings_created}"
            building.scale = (width, depth, height)
            
            # Apply random material
            building.data.materials.append(random.choice(materials))
            
            # Add architectural details
            if random.random() > 0.6:
                # Add antenna or roof structure
                bpy.ops.mesh.primitive_cylinder_add(radius=0.1, depth=2, location=(x, y, height + 1))
                antenna = bpy.context.active_object
                antenna.name = f"Antenna_{buildings_created}"
                antenna.data.materials.append(metal_mat)
            
            buildings_created += 1

print(f"Created {buildings_created} buildings with advanced materials")
"""
    result = client.execute_code(buildings_code)
    print(f"Buildings result: {result}")
    
    # Step 4: Advanced lighting setup
    print("Step 4: Setting up advanced lighting...")
    lighting_code = """
# Remove default light
if 'Light' in bpy.data.objects:
    bpy.data.objects.remove(bpy.data.objects['Light'], do_unlink=True)

# Sun light
bpy.ops.object.light_add(type='SUN', location=(20, 20, 30))
sun = bpy.context.active_object
sun.name = "MainSun"
sun.data.energy = 5.0
sun.data.color = (1.0, 0.95, 0.8)
sun.rotation_euler = (math.radians(45), 0, math.radians(45))

# Area lights for ambient lighting
positions = [(-25, -25, 20), (25, -25, 20), (-25, 25, 20), (25, 25, 20)]
for i, pos in enumerate(positions):
    bpy.ops.object.light_add(type='AREA', location=pos)
    area_light = bpy.context.active_object
    area_light.name = f"AreaLight_{i}"
    area_light.data.energy = 150.0
    area_light.data.size = 15.0
    area_light.data.color = (0.8, 0.9, 1.0)
    
    # Point towards center
    direction = Vector((0, 0, 0)) - Vector(pos)
    area_light.rotation_euler = direction.to_track_quat('-Z', 'Y').to_euler()

# Street lights
street_lights = 0
for i in range(-32, 33, 16):
    for j in range(-32, 33, 16):
        if abs(i) % 8 == 0 or abs(j) % 8 == 0:  # Only on roads
            bpy.ops.object.light_add(type='POINT', location=(i, j, 5))
            street_light = bpy.context.active_object
            street_light.name = f"StreetLight_{i}_{j}"
            street_light.data.energy = 100.0
            street_light.data.color = (1.0, 0.9, 0.7)
            street_light.data.shadow_soft_size = 1.0
            street_lights += 1

print(f"Created advanced lighting setup with {street_lights} street lights")
"""
    result = client.execute_code(lighting_code)
    print(f"Lighting result: {result}")
    
    # Step 5: Particle systems and effects
    print("Step 5: Creating particle systems...")
    particles_code = """
# Fog/atmosphere system
bpy.ops.mesh.primitive_plane_add(size=100, location=(0, 0, 2))
fog_emitter = bpy.context.active_object
fog_emitter.name = "FogEmitter"

fog_emitter.modifiers.new("ParticleSystem", 'PARTICLE_SYSTEM')
psys = fog_emitter.particle_systems[0]
psys.settings.type = 'EMITTER'
psys.settings.count = 1500
psys.settings.emit_from = 'FACE'
psys.settings.physics_type = 'NO'
psys.settings.particle_size = 3.0
psys.settings.size_random = 0.7
psys.settings.lifetime = 300
psys.settings.normal_factor = 0.2

# Rain system
bpy.ops.mesh.primitive_plane_add(size=120, location=(0, 0, 60))
rain_emitter = bpy.context.active_object
rain_emitter.name = "RainEmitter"

rain_emitter.modifiers.new("ParticleSystem", 'PARTICLE_SYSTEM')
rain_psys = rain_emitter.particle_systems[0]
rain_psys.settings.type = 'EMITTER'
rain_psys.settings.count = 3000
rain_psys.settings.emit_from = 'FACE'
rain_psys.settings.physics_type = 'NEWTONIAN'
rain_psys.settings.particle_size = 0.03
rain_psys.settings.lifetime = 120
rain_psys.settings.normal_factor = 0
rain_psys.settings.factor_random = 0
rain_psys.settings.effector_weights.gravity = 3.0

print("Created particle systems for fog and rain")
"""
    result = client.execute_code(particles_code)
    print(f"Particles result: {result}")
    
    # Step 6: Camera animation
    print("Step 6: Setting up camera animation...")
    camera_code = """
# Remove default camera
if 'Camera' in bpy.data.objects:
    bpy.data.objects.remove(bpy.data.objects['Camera'], do_unlink=True)

# Create new camera
bpy.ops.object.camera_add(location=(60, -60, 30))
camera = bpy.context.active_object
camera.name = "AnimatedCamera"
bpy.context.scene.camera = camera

# Create camera animation
keyframes = [
    (1, (60, -60, 30), (math.radians(65), 0, math.radians(45))),
    (60, (0, -80, 35), (math.radians(70), 0, math.radians(90))),
    (120, (-60, -40, 25), (math.radians(60), 0, math.radians(135))),
    (180, (-40, 40, 40), (math.radians(55), 0, math.radians(225))),
    (240, (40, 60, 30), (math.radians(65), 0, math.radians(315))),
    (300, (60, -60, 30), (math.radians(65), 0, math.radians(45))),
]

for frame, location, rotation in keyframes:
    bpy.context.scene.frame_set(frame)
    camera.location = location
    camera.rotation_euler = rotation
    camera.keyframe_insert(data_path="location")
    camera.keyframe_insert(data_path="rotation_euler")

bpy.context.scene.frame_start = 1
bpy.context.scene.frame_end = 300

print("Created 300-frame camera animation")
"""
    result = client.execute_code(camera_code)
    print(f"Camera result: {result}")
    
    # Step 7: Render settings
    print("Step 7: Configuring render settings...")
    render_code = """
# Set render engine to Cycles
bpy.context.scene.render.engine = 'CYCLES'

# Quality settings
bpy.context.scene.cycles.samples = 64
bpy.context.scene.render.resolution_x = 1920
bpy.context.scene.render.resolution_y = 1080
bpy.context.scene.render.resolution_percentage = 100

# Enable denoising
bpy.context.scene.cycles.use_denoising = True

# Set output format
bpy.context.scene.render.image_settings.file_format = 'PNG'
bpy.context.scene.render.filepath = "//ultra_complex_scene"

print("Render settings configured for high quality output")
"""
    result = client.execute_code(render_code)
    print(f"Render settings result: {result}")
    
    # Get final scene info
    print("Getting final scene information...")
    scene_info = client.get_scene_info()
    print(f"Final scene info: {scene_info}")
    
    client.disconnect()
    
    print("\n" + "="*60)
    print("ULTRA-COMPLEX SCENE CREATION COMPLETED!")
    print("="*60)
    print("Scene features:")
    print("✓ Procedural city with road network")
    print("✓ 50+ buildings with advanced materials")
    print("✓ Glass, concrete, and metal materials with shader nodes")
    print("✓ Advanced lighting (sun + area lights + street lights)")
    print("✓ Particle systems (fog and rain)")
    print("✓ 300-frame camera animation")
    print("✓ High-quality Cycles render settings")
    print("✓ Procedural textures and noise")
    print("="*60)

if __name__ == "__main__":
    create_ultra_complex_scene()
