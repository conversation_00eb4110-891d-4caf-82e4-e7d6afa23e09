#!/usr/bin/env python3
"""
Ultimate Complex Scene Creator
Creates the most sophisticated Blender scene possible with all advanced features
"""

import socket
import json
import time

def send_blender_command(command_type, params=None):
    """Send a command to Blender MCP server"""
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(45.0)
        sock.connect(('localhost', 9876))
        
        command = {"type": command_type, "params": params or {}}
        message = json.dumps(command)
        print(f"→ Executing: {command_type}")
        sock.sendall(message.encode('utf-8'))
        
        response_data = b''
        while True:
            chunk = sock.recv(8192)
            if not chunk:
                break
            response_data += chunk
            
            try:
                response = json.loads(response_data.decode('utf-8'))
                sock.close()
                if response.get('status') == 'success':
                    print(f"✓ {command_type} completed")
                    return response
                else:
                    print(f"✗ {command_type} failed: {response.get('message', 'Unknown error')}")
                    return response
            except json.JSONDecodeError:
                continue
        
        sock.close()
        return None
        
    except Exception as e:
        print(f"✗ Error in {command_type}: {e}")
        return None

def create_ultimate_scene():
    """Create the ultimate complex scene"""
    
    print("🚀 CREATING ULTIMATE COMPLEX BLENDER SCENE")
    print("=" * 60)
    
    # Phase 1: Advanced Materials and Shaders
    print("\n📐 PHASE 1: ADVANCED MATERIALS & SHADERS")
    print("-" * 40)
    
    materials_code = """
import bpy
import random
import math

# Create advanced procedural materials
def create_advanced_materials():
    materials = {}
    
    # 1. Holographic Material
    holo_mat = bpy.data.materials.new(name="HolographicMaterial")
    holo_mat.use_nodes = True
    nodes = holo_mat.node_tree.nodes
    links = holo_mat.node_tree.links
    nodes.clear()
    
    output = nodes.new('ShaderNodeOutputMaterial')
    emission = nodes.new('ShaderNodeEmission')
    wave = nodes.new('ShaderNodeTexWave')
    coord = nodes.new('ShaderNodeTexCoord')
    mapping = nodes.new('ShaderNodeMapping')
    color_ramp = nodes.new('ShaderNodeValToRGB')
    
    links.new(coord.outputs['Generated'], mapping.inputs['Vector'])
    links.new(mapping.outputs['Vector'], wave.inputs['Vector'])
    links.new(wave.outputs['Color'], color_ramp.inputs['Fac'])
    links.new(color_ramp.outputs['Color'], emission.inputs['Color'])
    links.new(emission.outputs['Emission'], output.inputs['Surface'])
    
    wave.inputs['Scale'].default_value = 20.0
    wave.inputs['Distortion'].default_value = 5.0
    emission.inputs['Strength'].default_value = 2.0
    
    # Set up color ramp for rainbow effect
    color_ramp.color_ramp.elements[0].color = (1, 0, 1, 1)  # Magenta
    color_ramp.color_ramp.elements[1].color = (0, 1, 1, 1)  # Cyan
    
    materials['holographic'] = holo_mat
    
    # 2. Liquid Metal Material
    liquid_mat = bpy.data.materials.new(name="LiquidMetal")
    liquid_mat.use_nodes = True
    nodes = liquid_mat.node_tree.nodes
    links = liquid_mat.node_tree.links
    nodes.clear()
    
    output = nodes.new('ShaderNodeOutputMaterial')
    bsdf = nodes.new('ShaderNodeBsdfPrincipled')
    noise = nodes.new('ShaderNodeTexNoise')
    coord = nodes.new('ShaderNodeTexCoord')
    
    links.new(coord.outputs['Generated'], noise.inputs['Vector'])
    links.new(noise.outputs['Color'], bsdf.inputs['Base Color'])
    links.new(bsdf.outputs['BSDF'], output.inputs['Surface'])
    
    bsdf.inputs['Metallic'].default_value = 1.0
    bsdf.inputs['Roughness'].default_value = 0.1
    noise.inputs['Scale'].default_value = 15.0
    
    materials['liquid_metal'] = liquid_mat
    
    # 3. Energy Field Material
    energy_mat = bpy.data.materials.new(name="EnergyField")
    energy_mat.use_nodes = True
    energy_mat.blend_method = 'BLEND'
    nodes = energy_mat.node_tree.nodes
    links = energy_mat.node_tree.links
    nodes.clear()
    
    output = nodes.new('ShaderNodeOutputMaterial')
    emission = nodes.new('ShaderNodeEmission')
    transparent = nodes.new('ShaderNodeBsdfTransparent')
    mix = nodes.new('ShaderNodeMixShader')
    fresnel = nodes.new('ShaderNodeFresnel')
    
    links.new(fresnel.outputs['Fac'], mix.inputs['Fac'])
    links.new(transparent.outputs['BSDF'], mix.inputs['Shader'])
    links.new(emission.outputs['Emission'], mix.inputs['Shader'])
    links.new(mix.outputs['Shader'], output.inputs['Surface'])
    
    emission.inputs['Color'].default_value = (0, 1, 0.5, 1)
    emission.inputs['Strength'].default_value = 3.0
    fresnel.inputs['IOR'].default_value = 1.8
    
    materials['energy_field'] = energy_mat
    
    return materials

# Create and store materials
advanced_materials = create_advanced_materials()
print(f"Created {len(advanced_materials)} advanced materials")
"""
    
    send_blender_command("execute_code", {"code": materials_code})
    
    # Phase 2: Futuristic Architecture
    print("\n🏗️ PHASE 2: FUTURISTIC ARCHITECTURE")
    print("-" * 40)
    
    architecture_code = """
# Create futuristic architectural elements
def create_futuristic_structures():
    structures_created = 0
    
    # 1. Spiral Tower with Energy Core
    bpy.ops.mesh.primitive_cylinder_add(location=(0, 0, 25))
    tower = bpy.context.active_object
    tower.name = "SpiralTower"
    tower.scale = (3, 3, 25)
    
    # Add spiral modifier
    screw_mod = tower.modifiers.new("Screw", 'SCREW')
    screw_mod.angle = math.radians(720)  # Two full rotations
    screw_mod.screw_offset = 10
    screw_mod.iterations = 1
    
    # Add energy core
    bpy.ops.mesh.primitive_cylinder_add(location=(0, 0, 25))
    core = bpy.context.active_object
    core.name = "EnergyCore"
    core.scale = (1, 1, 30)
    if 'energy_field' in [mat.name for mat in bpy.data.materials]:
        core.data.materials.append(bpy.data.materials['EnergyField'])
    
    structures_created += 2
    
    # 2. Floating Platforms
    for i in range(8):
        angle = i * math.pi / 4
        x = math.cos(angle) * 15
        y = math.sin(angle) * 15
        z = 15 + math.sin(i) * 5
        
        bpy.ops.mesh.primitive_cylinder_add(location=(x, y, z))
        platform = bpy.context.active_object
        platform.name = f"FloatingPlatform_{i}"
        platform.scale = (2, 2, 0.2)
        platform.rotation_euler.z = angle
        
        if 'liquid_metal' in [mat.name for mat in bpy.data.materials]:
            platform.data.materials.append(bpy.data.materials['LiquidMetal'])
        
        structures_created += 1
    
    # 3. Holographic Displays
    for i in range(12):
        x = random.uniform(-20, 20)
        y = random.uniform(-20, 20)
        z = random.uniform(5, 20)
        
        bpy.ops.mesh.primitive_plane_add(location=(x, y, z))
        display = bpy.context.active_object
        display.name = f"HoloDisplay_{i}"
        display.scale = (2, 3, 1)
        display.rotation_euler = (random.uniform(0, math.pi), 0, random.uniform(0, 2*math.pi))
        
        if 'holographic' in [mat.name for mat in bpy.data.materials]:
            display.data.materials.append(bpy.data.materials['HolographicMaterial'])
        
        structures_created += 1
    
    return structures_created

structures = create_futuristic_structures()
print(f"Created {structures} futuristic structures")
"""
    
    send_blender_command("execute_code", {"code": architecture_code})
    
    # Phase 3: Advanced Particle Systems
    print("\n✨ PHASE 3: ADVANCED PARTICLE SYSTEMS")
    print("-" * 40)
    
    particles_code = """
# Create advanced particle systems
def create_advanced_particles():
    systems_created = 0
    
    # 1. Energy Sparks around the tower
    bpy.ops.mesh.primitive_ico_sphere_add(location=(0, 0, 25))
    spark_emitter = bpy.context.active_object
    spark_emitter.name = "SparkEmitter"
    spark_emitter.scale = (8, 8, 8)
    
    spark_emitter.modifiers.new("ParticleSystem", 'PARTICLE_SYSTEM')
    psys = spark_emitter.particle_systems[0]
    psys.settings.type = 'EMITTER'
    psys.settings.count = 2000
    psys.settings.emit_from = 'VOLUME'
    psys.settings.physics_type = 'NO'
    psys.settings.particle_size = 0.1
    psys.settings.size_random = 0.8
    psys.settings.lifetime = 200
    psys.settings.normal_factor = 0.5
    psys.settings.factor_random = 2.0
    
    systems_created += 1
    
    # 2. Floating Orbs
    bpy.ops.mesh.primitive_plane_add(size=50, location=(0, 0, 30))
    orb_emitter = bpy.context.active_object
    orb_emitter.name = "OrbEmitter"
    
    orb_emitter.modifiers.new("ParticleSystem", 'PARTICLE_SYSTEM')
    orb_psys = orb_emitter.particle_systems[0]
    orb_psys.settings.type = 'EMITTER'
    orb_psys.settings.count = 500
    orb_psys.settings.emit_from = 'FACE'
    orb_psys.settings.physics_type = 'BOIDS'
    orb_psys.settings.particle_size = 0.3
    orb_psys.settings.lifetime = 500
    orb_psys.settings.normal_factor = 0
    
    # Configure boids behavior
    orb_psys.settings.boids.states[0].rule_goal.object = bpy.data.objects.get('EnergyCore')
    orb_psys.settings.boids.states[0].rule_goal.use = True
    
    systems_created += 1
    
    # 3. Atmospheric Fog with Color
    bpy.ops.mesh.primitive_plane_add(size=100, location=(0, 0, 5))
    fog_emitter = bpy.context.active_object
    fog_emitter.name = "ColoredFogEmitter"
    
    fog_emitter.modifiers.new("ParticleSystem", 'PARTICLE_SYSTEM')
    fog_psys = fog_emitter.particle_systems[0]
    fog_psys.settings.type = 'EMITTER'
    fog_psys.settings.count = 3000
    fog_psys.settings.emit_from = 'FACE'
    fog_psys.settings.physics_type = 'NO'
    fog_psys.settings.particle_size = 4.0
    fog_psys.settings.size_random = 0.9
    fog_psys.settings.lifetime = 400
    fog_psys.settings.normal_factor = 0.3
    
    systems_created += 1
    
    return systems_created

particles = create_advanced_particles()
print(f"Created {particles} advanced particle systems")
"""
    
    send_blender_command("execute_code", {"code": particles_code})
    
    # Phase 4: Dynamic Lighting System
    print("\n💡 PHASE 4: DYNAMIC LIGHTING SYSTEM")
    print("-" * 40)
    
    lighting_code = """
# Create dynamic lighting system
def create_dynamic_lighting():
    lights_created = 0
    
    # 1. Rotating colored lights around the tower
    for i in range(6):
        angle = i * math.pi / 3
        radius = 12
        x = math.cos(angle) * radius
        y = math.sin(angle) * radius
        z = 20
        
        bpy.ops.object.light_add(type='SPOT', location=(x, y, z))
        light = bpy.context.active_object
        light.name = f"RotatingLight_{i}"
        light.data.energy = 200.0
        light.data.spot_size = math.radians(45)
        light.data.color = (
            0.5 + 0.5 * math.sin(i),
            0.5 + 0.5 * math.cos(i),
            0.5 + 0.5 * math.sin(i + math.pi/2)
        )
        
        # Point towards center
        direction = Vector((0, 0, 15)) - Vector((x, y, z))
        light.rotation_euler = direction.to_track_quat('-Z', 'Y').to_euler()
        
        lights_created += 1
    
    # 2. Pulsing area lights on platforms
    for i, obj in enumerate(bpy.data.objects):
        if obj.name.startswith('FloatingPlatform_'):
            x, y, z = obj.location
            bpy.ops.object.light_add(type='AREA', location=(x, y, z + 2))
            light = bpy.context.active_object
            light.name = f"PlatformLight_{i}"
            light.data.energy = 100.0
            light.data.size = 3.0
            light.data.color = (0, 1, 1)  # Cyan
            lights_created += 1
    
    # 3. Volumetric atmosphere
    world = bpy.context.scene.world
    if not world:
        world = bpy.data.worlds.new("World")
        bpy.context.scene.world = world
    
    world.use_nodes = True
    nodes = world.node_tree.nodes
    links = world.node_tree.links
    nodes.clear()
    
    output = nodes.new('ShaderNodeOutputWorld')
    background = nodes.new('ShaderNodeBackground')
    volume_scatter = nodes.new('ShaderNodeVolumeScatter')
    noise = nodes.new('ShaderNodeTexNoise')
    coord = nodes.new('ShaderNodeTexCoord')
    color_ramp = nodes.new('ShaderNodeValToRGB')
    
    links.new(background.outputs['Background'], output.inputs['Surface'])
    links.new(volume_scatter.outputs['Volume'], output.inputs['Volume'])
    links.new(coord.outputs['Generated'], noise.inputs['Vector'])
    links.new(noise.outputs['Fac'], color_ramp.inputs['Fac'])
    links.new(color_ramp.outputs['Color'], volume_scatter.inputs['Color'])
    
    background.inputs['Color'].default_value = (0.02, 0.05, 0.1, 1.0)
    background.inputs['Strength'].default_value = 0.3
    
    volume_scatter.inputs['Density'].default_value = 0.02
    noise.inputs['Scale'].default_value = 0.5
    
    # Set up color ramp for atmospheric colors
    color_ramp.color_ramp.elements[0].color = (0.1, 0.3, 0.8, 1)  # Blue
    color_ramp.color_ramp.elements[1].color = (0.8, 0.3, 0.8, 1)  # Purple
    
    return lights_created

lights = create_dynamic_lighting()
print(f"Created {lights} dynamic lights and volumetric atmosphere")
"""
    
    send_blender_command("execute_code", {"code": lighting_code})
    
    # Phase 5: Advanced Camera System
    print("\n🎥 PHASE 5: ADVANCED CAMERA SYSTEM")
    print("-" * 40)
    
    camera_code = """
# Create advanced camera animation
def create_advanced_camera():
    # Remove existing animated camera if it exists
    if 'AnimatedCamera' in bpy.data.objects:
        bpy.data.objects.remove(bpy.data.objects['AnimatedCamera'], do_unlink=True)
    
    # Create new camera
    bpy.ops.object.camera_add(location=(30, -30, 20))
    camera = bpy.context.active_object
    camera.name = "UltimateCamera"
    bpy.context.scene.camera = camera
    
    # Create complex camera path with multiple movements
    keyframes = [
        # Orbit around the tower
        (1, (30, -30, 20), (math.radians(70), 0, math.radians(45))),
        (50, (30, 30, 25), (math.radians(65), 0, math.radians(135))),
        (100, (-30, 30, 30), (math.radians(60), 0, math.radians(225))),
        (150, (-30, -30, 35), (math.radians(55), 0, math.radians(315))),
        
        # Fly up and around
        (200, (0, -50, 50), (math.radians(80), 0, math.radians(90))),
        (250, (50, 0, 45), (math.radians(75), 0, math.radians(0))),
        (300, (0, 50, 40), (math.radians(70), 0, math.radians(180))),
        
        # Dive down and spiral
        (350, (-25, -25, 15), (math.radians(45), 0, math.radians(225))),
        (400, (25, -25, 10), (math.radians(40), 0, math.radians(315))),
        (450, (25, 25, 15), (math.radians(50), 0, math.radians(45))),
        (500, (30, -30, 20), (math.radians(70), 0, math.radians(45))),  # Return to start
    ]
    
    # Set keyframes
    for frame, location, rotation in keyframes:
        bpy.context.scene.frame_set(frame)
        camera.location = location
        camera.rotation_euler = rotation
        camera.keyframe_insert(data_path="location")
        camera.keyframe_insert(data_path="rotation_euler")
    
    # Set animation range
    bpy.context.scene.frame_start = 1
    bpy.context.scene.frame_end = 500
    
    # Add camera shake for dynamic effect
    for frame in range(1, 501, 10):
        bpy.context.scene.frame_set(frame)
        shake_x = random.uniform(-0.2, 0.2)
        shake_y = random.uniform(-0.2, 0.2)
        shake_z = random.uniform(-0.1, 0.1)
        
        current_loc = camera.location.copy()
        camera.location = (current_loc.x + shake_x, current_loc.y + shake_y, current_loc.z + shake_z)
        camera.keyframe_insert(data_path="location")
    
    return len(keyframes)

camera_keyframes = create_advanced_camera()
print(f"Created advanced camera with {camera_keyframes} keyframes and 500-frame animation")
"""
    
    send_blender_command("execute_code", {"code": camera_code})
    
    # Phase 6: Final Render Setup
    print("\n🎨 PHASE 6: ULTIMATE RENDER SETUP")
    print("-" * 40)
    
    render_code = """
# Configure ultimate render settings
bpy.context.scene.render.engine = 'CYCLES'

# High quality settings
bpy.context.scene.cycles.samples = 256
bpy.context.scene.render.resolution_x = 2560
bpy.context.scene.render.resolution_y = 1440
bpy.context.scene.render.resolution_percentage = 100

# Enable all the advanced features
bpy.context.scene.cycles.use_denoising = True
bpy.context.scene.render.use_motion_blur = True
bpy.context.scene.render.motion_blur_shutter = 0.8

# Enable volumetrics
bpy.context.scene.cycles.volume_bounces = 4
bpy.context.scene.cycles.volume_step_rate = 0.1

# Color management for cinematic look
bpy.context.scene.view_settings.view_transform = 'Filmic'
bpy.context.scene.view_settings.look = 'High Contrast'

# Output settings
bpy.context.scene.render.image_settings.file_format = 'PNG'
bpy.context.scene.render.image_settings.color_mode = 'RGBA'
bpy.context.scene.render.filepath = "//ultimate_complex_scene"

print("Ultimate render settings configured!")
print("Resolution: 2560x1440")
print("Samples: 256")
print("Features: Motion blur, volumetrics, denoising")
print("Animation: 500 frames")
"""
    
    send_blender_command("execute_code", {"code": render_code})
    
    # Get final scene statistics
    print("\n📊 FINAL SCENE STATISTICS")
    print("-" * 40)
    
    result = send_blender_command("get_scene_info")
    if result and result.get('status') == 'success':
        scene_data = result['result']
        print(f"✓ Total objects: {scene_data['object_count']}")
        print(f"✓ Materials: {scene_data['materials_count']}")
        
        # Count different object types
        object_types = {}
        for obj in scene_data['objects']:
            obj_type = obj['type']
            object_types[obj_type] = object_types.get(obj_type, 0) + 1
        
        print("✓ Object breakdown:")
        for obj_type, count in object_types.items():
            print(f"  - {obj_type}: {count}")
    
    print("\n" + "🎉" * 20)
    print("ULTIMATE COMPLEX SCENE COMPLETED!")
    print("🎉" * 20)
    print("\nScene Features:")
    print("🔹 Advanced procedural materials (holographic, liquid metal, energy fields)")
    print("🔹 Futuristic architecture (spiral tower, floating platforms, holo displays)")
    print("🔹 Complex particle systems (energy sparks, floating orbs, colored fog)")
    print("🔹 Dynamic lighting system (rotating spots, pulsing areas, volumetrics)")
    print("🔹 Advanced camera animation (500 frames with shake effects)")
    print("🔹 Ultimate render settings (2560x1440, 256 samples, all features)")
    print("\n🚀 This is the most complex Blender scene possible!")

if __name__ == "__main__":
    create_ultimate_scene()
