#!/usr/bin/env python3
"""
Check current scene status and take screenshot
"""

import socket
import json
import tempfile
import os

def send_blender_command(command_type, params=None):
    """Send a command to Blender MCP server"""
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(30.0)
        sock.connect(('localhost', 9876))
        
        command = {"type": command_type, "params": params or {}}
        message = json.dumps(command)
        sock.sendall(message.encode('utf-8'))
        
        response_data = b''
        while True:
            chunk = sock.recv(8192)
            if not chunk:
                break
            response_data += chunk
            
            try:
                response = json.loads(response_data.decode('utf-8'))
                sock.close()
                return response
            except json.JSONDecodeError:
                continue
        
        sock.close()
        return None
        
    except Exception as e:
        print(f"Error: {e}")
        return None

def main():
    print("🔍 CHECKING CURRENT SCENE STATUS")
    print("=" * 40)
    
    # Get scene info
    result = send_blender_command("get_scene_info")
    if result and result.get('status') == 'success':
        scene_data = result['result']
        print(f"📊 Current scene statistics:")
        print(f"   Total objects: {scene_data['object_count']}")
        print(f"   Materials: {scene_data['materials_count']}")
        
        print(f"\n📦 Objects in scene:")
        for i, obj in enumerate(scene_data['objects']):
            if i >= 20:  # Show first 20
                print(f"   ... and {scene_data['object_count'] - 20} more objects")
                break
            print(f"   {i+1:2d}. {obj['name']} ({obj['type']}) at {obj['location']}")
    
    # Take a screenshot
    print(f"\n📸 Taking viewport screenshot...")
    temp_path = os.path.join(tempfile.gettempdir(), "blender_scene_screenshot.png")
    
    screenshot_result = send_blender_command("get_viewport_screenshot", {
        "max_size": 1200,
        "filepath": temp_path,
        "format": "png"
    })
    
    if screenshot_result and screenshot_result.get('status') == 'success':
        result_data = screenshot_result['result']
        if result_data.get('success'):
            print(f"✅ Screenshot saved successfully!")
            print(f"   File: {temp_path}")
            print(f"   Size: {result_data.get('width')}x{result_data.get('height')}")
        else:
            print(f"❌ Screenshot failed: {result_data.get('error')}")
    else:
        print(f"❌ Screenshot command failed")
    
    print(f"\n🎬 SCENE STATUS COMPLETE")
    print("=" * 40)

if __name__ == "__main__":
    main()
