#!/usr/bin/env python3
"""
Simple scene creation test
"""

import socket
import json
import sys

def send_blender_command(command_type, params=None):
    """Send a single command to Blender"""
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(30.0)
        sock.connect(('localhost', 9876))
        
        command = {
            "type": command_type,
            "params": params or {}
        }
        
        message = json.dumps(command)
        print(f"Sending: {command_type}")
        sock.sendall(message.encode('utf-8'))
        
        # Receive response
        response_data = b''
        while True:
            chunk = sock.recv(8192)
            if not chunk:
                break
            response_data += chunk
            
            try:
                response = json.loads(response_data.decode('utf-8'))
                sock.close()
                return response
            except json.JSONDecodeError:
                continue
        
        sock.close()
        return None
        
    except Exception as e:
        print(f"Error: {e}")
        return None

def main():
    print("Testing Blender MCP connection...")
    
    # Test 1: Get scene info
    print("\n1. Getting scene info...")
    result = send_blender_command("get_scene_info")
    if result:
        print(f"✓ Scene has {result['result']['object_count']} objects")
    else:
        print("✗ Failed to get scene info")
        return
    
    # Test 2: Execute simple code
    print("\n2. Creating a simple cube...")
    code = """
import bpy
bpy.ops.mesh.primitive_cube_add(location=(2, 2, 2))
cube = bpy.context.active_object
cube.name = "TestCube"
cube.scale = (2, 2, 2)
print("Created test cube successfully!")
"""
    
    result = send_blender_command("execute_code", {"code": code})
    if result and result.get('status') == 'success':
        print("✓ Test cube created successfully!")
        print(f"Result: {result['result']['result']}")
    else:
        print("✗ Failed to create test cube")
        print(f"Error: {result}")
        return
    
    # Test 3: Get updated scene info
    print("\n3. Getting updated scene info...")
    result = send_blender_command("get_scene_info")
    if result:
        print(f"✓ Scene now has {result['result']['object_count']} objects")
        for obj in result['result']['objects']:
            print(f"  - {obj['name']} ({obj['type']}) at {obj['location']}")
    else:
        print("✗ Failed to get updated scene info")
    
    # Test 4: Create a more complex scene
    print("\n4. Creating complex scene elements...")
    complex_code = """
import bpy
import random
import math

# Clear existing objects except camera and light
for obj in bpy.context.scene.objects:
    if obj.type == 'MESH':
        bpy.data.objects.remove(obj, do_unlink=True)

# Create ground plane
bpy.ops.mesh.primitive_plane_add(size=20, location=(0, 0, 0))
ground = bpy.context.active_object
ground.name = "Ground"

# Create some buildings
buildings_created = 0
for i in range(5):
    for j in range(5):
        x = (i - 2) * 4
        y = (j - 2) * 4
        height = random.uniform(2, 8)
        
        bpy.ops.mesh.primitive_cube_add(location=(x, y, height/2))
        building = bpy.context.active_object
        building.name = f"Building_{i}_{j}"
        building.scale = (1.5, 1.5, height)
        buildings_created += 1

# Add some lights
bpy.ops.object.light_add(type='SUN', location=(10, 10, 10))
sun = bpy.context.active_object
sun.name = "Sun"
sun.data.energy = 3.0

print(f"Created complex scene with {buildings_created} buildings!")
"""
    
    result = send_blender_command("execute_code", {"code": complex_code})
    if result and result.get('status') == 'success':
        print("✓ Complex scene created successfully!")
        print(f"Result: {result['result']['result']}")
    else:
        print("✗ Failed to create complex scene")
        print(f"Error: {result}")
        return
    
    # Final scene info
    print("\n5. Final scene info...")
    result = send_blender_command("get_scene_info")
    if result:
        print(f"✓ Final scene has {result['result']['object_count']} objects")
        print("Objects in scene:")
        for obj in result['result']['objects'][:10]:  # Show first 10
            print(f"  - {obj['name']} ({obj['type']}) at {obj['location']}")
        if result['result']['object_count'] > 10:
            print(f"  ... and {result['result']['object_count'] - 10} more objects")
    
    print("\n" + "="*50)
    print("SCENE CREATION TEST COMPLETED SUCCESSFULLY!")
    print("="*50)

if __name__ == "__main__":
    main()
