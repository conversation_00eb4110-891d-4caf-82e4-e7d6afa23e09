#!/usr/bin/env python3
"""
Final Ultimate Complex Scene - Fixed Version
Creates the most sophisticated Blender scene with proper imports
"""

import socket
import json

def send_blender_command(command_type, params=None):
    """Send a command to Blender MCP server"""
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(45.0)
        sock.connect(('localhost', 9876))
        
        command = {"type": command_type, "params": params or {}}
        message = json.dumps(command)
        print(f"→ {command_type}")
        sock.sendall(message.encode('utf-8'))
        
        response_data = b''
        while True:
            chunk = sock.recv(8192)
            if not chunk:
                break
            response_data += chunk
            
            try:
                response = json.loads(response_data.decode('utf-8'))
                sock.close()
                if response.get('status') == 'success':
                    print(f"✓ {command_type} completed")
                    return response
                else:
                    print(f"✗ {command_type} failed: {response.get('message', 'Unknown error')}")
                    return response
            except json.JSONDecodeError:
                continue
        
        sock.close()
        return None
        
    except Exception as e:
        print(f"✗ Error in {command_type}: {e}")
        return None

def create_final_ultimate_scene():
    """Create the final ultimate complex scene"""
    
    print("🌟 FINAL ULTIMATE COMPLEX BLENDER SCENE")
    print("=" * 60)
    
    # Complete scene with all imports included
    ultimate_scene_code = """
import bpy
import bmesh
import mathutils
from mathutils import Vector, Matrix, Euler
import random
import math

# Clear scene first
bpy.ops.object.select_all(action='SELECT')
bpy.ops.object.delete(use_global=False, confirm=False)

# Clear materials
for material in bpy.data.materials:
    bpy.data.materials.remove(material)

print("Scene cleared, creating ultimate complex scene...")

# === PHASE 1: ADVANCED MATERIALS ===
def create_ultimate_materials():
    materials = {}
    
    # Holographic Material
    holo_mat = bpy.data.materials.new(name="Holographic")
    holo_mat.use_nodes = True
    nodes = holo_mat.node_tree.nodes
    links = holo_mat.node_tree.links
    nodes.clear()
    
    output = nodes.new('ShaderNodeOutputMaterial')
    emission = nodes.new('ShaderNodeEmission')
    wave = nodes.new('ShaderNodeTexWave')
    coord = nodes.new('ShaderNodeTexCoord')
    
    links.new(coord.outputs['Generated'], wave.inputs['Vector'])
    links.new(wave.outputs['Color'], emission.inputs['Color'])
    links.new(emission.outputs['Emission'], output.inputs['Surface'])
    
    wave.inputs['Scale'].default_value = 15.0
    emission.inputs['Strength'].default_value = 3.0
    materials['holo'] = holo_mat
    
    # Liquid Metal Material
    metal_mat = bpy.data.materials.new(name="LiquidMetal")
    metal_mat.use_nodes = True
    nodes = metal_mat.node_tree.nodes
    links = metal_mat.node_tree.links
    nodes.clear()
    
    output = nodes.new('ShaderNodeOutputMaterial')
    bsdf = nodes.new('ShaderNodeBsdfPrincipled')
    noise = nodes.new('ShaderNodeTexNoise')
    coord = nodes.new('ShaderNodeTexCoord')
    
    links.new(coord.outputs['Generated'], noise.inputs['Vector'])
    links.new(noise.outputs['Color'], bsdf.inputs['Base Color'])
    links.new(bsdf.outputs['BSDF'], output.inputs['Surface'])
    
    bsdf.inputs['Metallic'].default_value = 1.0
    bsdf.inputs['Roughness'].default_value = 0.05
    noise.inputs['Scale'].default_value = 20.0
    materials['metal'] = metal_mat
    
    # Energy Field Material
    energy_mat = bpy.data.materials.new(name="EnergyField")
    energy_mat.use_nodes = True
    energy_mat.blend_method = 'BLEND'
    nodes = energy_mat.node_tree.nodes
    links = energy_mat.node_tree.links
    nodes.clear()
    
    output = nodes.new('ShaderNodeOutputMaterial')
    emission = nodes.new('ShaderNodeEmission')
    transparent = nodes.new('ShaderNodeBsdfTransparent')
    mix = nodes.new('ShaderNodeMixShader')
    fresnel = nodes.new('ShaderNodeFresnel')
    
    links.new(fresnel.outputs['Fac'], mix.inputs['Fac'])
    links.new(transparent.outputs['BSDF'], mix.inputs['Shader'])
    links.new(emission.outputs['Emission'], mix.inputs['Shader'])
    links.new(mix.outputs['Shader'], output.inputs['Surface'])
    
    emission.inputs['Color'].default_value = (0, 1, 0.5, 1)
    emission.inputs['Strength'].default_value = 4.0
    materials['energy'] = energy_mat
    
    return materials

materials = create_ultimate_materials()
print(f"Created {len(materials)} advanced materials")

# === PHASE 2: FUTURISTIC ARCHITECTURE ===
def create_mega_structures():
    structures = 0
    
    # Central Spiral Tower
    bpy.ops.mesh.primitive_cylinder_add(location=(0, 0, 30))
    tower = bpy.context.active_object
    tower.name = "MegaTower"
    tower.scale = (4, 4, 30)
    tower.data.materials.append(materials['metal'])
    
    # Add screw modifier for spiral
    screw_mod = tower.modifiers.new("Screw", 'SCREW')
    screw_mod.angle = math.radians(1080)  # 3 full rotations
    screw_mod.screw_offset = 15
    screw_mod.iterations = 1
    structures += 1
    
    # Energy Core
    bpy.ops.mesh.primitive_cylinder_add(location=(0, 0, 30))
    core = bpy.context.active_object
    core.name = "EnergyCore"
    core.scale = (1.5, 1.5, 35)
    core.data.materials.append(materials['energy'])
    structures += 1
    
    # Floating Ring Structures
    for ring in range(3):
        ring_height = 20 + ring * 15
        ring_radius = 15 + ring * 5
        
        for i in range(8):
            angle = i * math.pi / 4
            x = math.cos(angle) * ring_radius
            y = math.sin(angle) * ring_radius
            
            bpy.ops.mesh.primitive_torus_add(location=(x, y, ring_height))
            ring_obj = bpy.context.active_object
            ring_obj.name = f"FloatingRing_{ring}_{i}"
            ring_obj.scale = (2, 2, 0.5)
            ring_obj.rotation_euler.z = angle
            ring_obj.data.materials.append(materials['metal'])
            structures += 1
    
    # Holographic Panels
    for i in range(20):
        x = random.uniform(-25, 25)
        y = random.uniform(-25, 25)
        z = random.uniform(10, 50)
        
        bpy.ops.mesh.primitive_plane_add(location=(x, y, z))
        panel = bpy.context.active_object
        panel.name = f"HoloPanel_{i}"
        panel.scale = (3, 4, 1)
        panel.rotation_euler = (
            random.uniform(0, math.pi),
            random.uniform(0, math.pi),
            random.uniform(0, 2*math.pi)
        )
        panel.data.materials.append(materials['holo'])
        structures += 1
    
    return structures

structures = create_mega_structures()
print(f"Created {structures} mega structures")

# === PHASE 3: ADVANCED PARTICLE SYSTEMS ===
def create_particle_effects():
    effects = 0
    
    # Energy Sparks
    bpy.ops.mesh.primitive_ico_sphere_add(location=(0, 0, 30))
    spark_emitter = bpy.context.active_object
    spark_emitter.name = "SparkEmitter"
    spark_emitter.scale = (10, 10, 10)
    
    spark_emitter.modifiers.new("Particles", 'PARTICLE_SYSTEM')
    psys = spark_emitter.particle_systems[0]
    psys.settings.type = 'EMITTER'
    psys.settings.count = 3000
    psys.settings.emit_from = 'VOLUME'
    psys.settings.physics_type = 'NO'
    psys.settings.particle_size = 0.15
    psys.settings.size_random = 0.9
    psys.settings.lifetime = 250
    psys.settings.normal_factor = 1.0
    psys.settings.factor_random = 3.0
    effects += 1
    
    # Atmospheric Fog
    bpy.ops.mesh.primitive_plane_add(size=120, location=(0, 0, 10))
    fog_emitter = bpy.context.active_object
    fog_emitter.name = "FogEmitter"
    
    fog_emitter.modifiers.new("Particles", 'PARTICLE_SYSTEM')
    fog_psys = fog_emitter.particle_systems[0]
    fog_psys.settings.type = 'EMITTER'
    fog_psys.settings.count = 4000
    fog_psys.settings.emit_from = 'FACE'
    fog_psys.settings.physics_type = 'NO'
    fog_psys.settings.particle_size = 5.0
    fog_psys.settings.size_random = 0.8
    fog_psys.settings.lifetime = 500
    fog_psys.settings.normal_factor = 0.5
    effects += 1
    
    return effects

effects = create_particle_effects()
print(f"Created {effects} particle effects")

# === PHASE 4: DYNAMIC LIGHTING ===
def create_lighting_system():
    lights = 0
    
    # Main Sun
    bpy.ops.object.light_add(type='SUN', location=(30, 30, 50))
    sun = bpy.context.active_object
    sun.name = "MainSun"
    sun.data.energy = 8.0
    sun.data.color = (1.0, 0.9, 0.7)
    sun.rotation_euler = (math.radians(45), 0, math.radians(45))
    lights += 1
    
    # Rotating Colored Spots
    for i in range(8):
        angle = i * math.pi / 4
        radius = 20
        x = math.cos(angle) * radius
        y = math.sin(angle) * radius
        z = 25
        
        bpy.ops.object.light_add(type='SPOT', location=(x, y, z))
        light = bpy.context.active_object
        light.name = f"ColorSpot_{i}"
        light.data.energy = 300.0
        light.data.spot_size = math.radians(60)
        light.data.color = (
            0.5 + 0.5 * math.sin(i * 0.7),
            0.5 + 0.5 * math.cos(i * 0.9),
            0.5 + 0.5 * math.sin(i * 1.1)
        )
        
        # Point towards center
        direction = Vector((0, 0, 20)) - Vector((x, y, z))
        light.rotation_euler = direction.to_track_quat('-Z', 'Y').to_euler()
        lights += 1
    
    # Area lights for ambient
    positions = [(-30, -30, 40), (30, -30, 40), (-30, 30, 40), (30, 30, 40)]
    for i, pos in enumerate(positions):
        bpy.ops.object.light_add(type='AREA', location=pos)
        light = bpy.context.active_object
        light.name = f"AreaLight_{i}"
        light.data.energy = 200.0
        light.data.size = 20.0
        light.data.color = (0.7, 0.9, 1.0)
        lights += 1
    
    return lights

lights = create_lighting_system()
print(f"Created {lights} dynamic lights")

# === PHASE 5: VOLUMETRIC ATMOSPHERE ===
world = bpy.context.scene.world
if not world:
    world = bpy.data.worlds.new("UltimateWorld")
    bpy.context.scene.world = world

world.use_nodes = True
nodes = world.node_tree.nodes
links = world.node_tree.links
nodes.clear()

output = nodes.new('ShaderNodeOutputWorld')
background = nodes.new('ShaderNodeBackground')
volume_scatter = nodes.new('ShaderNodeVolumeScatter')
noise = nodes.new('ShaderNodeTexNoise')
coord = nodes.new('ShaderNodeTexCoord')

links.new(background.outputs['Background'], output.inputs['Surface'])
links.new(volume_scatter.outputs['Volume'], output.inputs['Volume'])
links.new(coord.outputs['Generated'], noise.inputs['Vector'])
links.new(noise.outputs['Color'], volume_scatter.inputs['Color'])

background.inputs['Color'].default_value = (0.01, 0.02, 0.05, 1.0)
background.inputs['Strength'].default_value = 0.2
volume_scatter.inputs['Density'].default_value = 0.03
noise.inputs['Scale'].default_value = 0.3

print("Created volumetric atmosphere")

# === PHASE 6: ULTIMATE CAMERA ===
# Remove existing cameras
for obj in bpy.context.scene.objects:
    if obj.type == 'CAMERA':
        bpy.data.objects.remove(obj, do_unlink=True)

bpy.ops.object.camera_add(location=(40, -40, 25))
camera = bpy.context.active_object
camera.name = "UltimateCamera"
bpy.context.scene.camera = camera

# Create epic camera animation
keyframes = [
    (1, (40, -40, 25), (math.radians(70), 0, math.radians(45))),
    (100, (40, 40, 30), (math.radians(65), 0, math.radians(135))),
    (200, (-40, 40, 35), (math.radians(60), 0, math.radians(225))),
    (300, (-40, -40, 40), (math.radians(55), 0, math.radians(315))),
    (400, (0, -60, 60), (math.radians(80), 0, math.radians(90))),
    (500, (60, 0, 50), (math.radians(75), 0, math.radians(0))),
    (600, (0, 60, 45), (math.radians(70), 0, math.radians(180))),
    (700, (40, -40, 25), (math.radians(70), 0, math.radians(45))),
]

for frame, location, rotation in keyframes:
    bpy.context.scene.frame_set(frame)
    camera.location = location
    camera.rotation_euler = rotation
    camera.keyframe_insert(data_path="location")
    camera.keyframe_insert(data_path="rotation_euler")

bpy.context.scene.frame_start = 1
bpy.context.scene.frame_end = 700

print(f"Created epic camera animation with {len(keyframes)} keyframes")

# === PHASE 7: ULTIMATE RENDER SETTINGS ===
bpy.context.scene.render.engine = 'CYCLES'
bpy.context.scene.cycles.samples = 512
bpy.context.scene.render.resolution_x = 3840
bpy.context.scene.render.resolution_y = 2160
bpy.context.scene.render.resolution_percentage = 100

bpy.context.scene.cycles.use_denoising = True
bpy.context.scene.render.use_motion_blur = True
bpy.context.scene.render.motion_blur_shutter = 1.0

bpy.context.scene.cycles.volume_bounces = 8
bpy.context.scene.cycles.volume_step_rate = 0.05

bpy.context.scene.view_settings.view_transform = 'Filmic'
bpy.context.scene.view_settings.look = 'Very High Contrast'

bpy.context.scene.render.image_settings.file_format = 'PNG'
bpy.context.scene.render.image_settings.color_mode = 'RGBA'
bpy.context.scene.render.filepath = "//ULTIMATE_COMPLEX_SCENE"

print("ULTIMATE COMPLEX SCENE COMPLETED!")
print("=" * 50)
print("FEATURES:")
print("✓ Advanced procedural materials")
print("✓ Futuristic mega-structures")
print("✓ Complex particle systems")
print("✓ Dynamic lighting system")
print("✓ Volumetric atmosphere")
print("✓ Epic 700-frame camera animation")
print("✓ 4K render settings (3840x2160)")
print("✓ 512 samples with all features")
print("=" * 50)
"""
    
    print("🎬 EXECUTING ULTIMATE SCENE CREATION...")
    print("-" * 60)
    
    result = send_blender_command("execute_code", {"code": ultimate_scene_code})
    
    if result and result.get('status') == 'success':
        print("\n🎊 SUCCESS! Ultimate scene created!")
        if 'result' in result and 'result' in result['result']:
            print(result['result']['result'])
    else:
        print(f"\n❌ Error: {result}")
    
    # Get final scene info
    print("\n📈 FINAL SCENE STATISTICS")
    print("-" * 40)
    
    scene_result = send_blender_command("get_scene_info")
    if scene_result and scene_result.get('status') == 'success':
        scene_data = scene_result['result']
        print(f"🎯 Total objects: {scene_data['object_count']}")
        print(f"🎨 Materials: {scene_data['materials_count']}")
        
        # Show object breakdown
        object_types = {}
        for obj in scene_data['objects'][:15]:  # Show first 15
            obj_type = obj['type']
            object_types[obj_type] = object_types.get(obj_type, 0) + 1
            print(f"  📦 {obj['name']} ({obj['type']}) at {obj['location']}")
        
        if scene_data['object_count'] > 15:
            print(f"  ... and {scene_data['object_count'] - 15} more objects")
    
    print("\n" + "🌟" * 30)
    print("🚀 ULTIMATE COMPLEX SCENE COMPLETED! 🚀")
    print("🌟" * 30)
    print("\n🎥 Ready for 4K rendering with 700-frame animation!")
    print("🎨 Most complex Blender scene ever created!")

if __name__ == "__main__":
    create_final_ultimate_scene()
