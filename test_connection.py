#!/usr/bin/env python3
"""
Simple test to check Blender MCP connection
"""

import socket
import json
import time

def test_connection():
    try:
        print("Attempting to connect to Blender MCP server on port 9876...")
        
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(5.0)
        sock.connect(('localhost', 9876))
        
        print("Connected successfully!")
        
        # Test simple command
        command = {
            "type": "get_scene_info",
            "params": {}
        }
        
        message = json.dumps(command)
        sock.sendall(message.encode('utf-8'))
        
        print("Command sent, waiting for response...")
        
        # Receive response
        response_data = b''
        sock.settimeout(10.0)
        
        while True:
            try:
                chunk = sock.recv(4096)
                if not chunk:
                    break
                response_data += chunk
                
                # Try to parse JSON
                try:
                    response = json.loads(response_data.decode('utf-8'))
                    print("Response received:")
                    print(json.dumps(response, indent=2))
                    break
                except json.JSONDecodeError:
                    continue
                    
            except socket.timeout:
                print("Timeout waiting for response")
                break
        
        sock.close()
        
    except ConnectionRefusedError:
        print("Connection refused. Make sure <PERSON><PERSON><PERSON> is running with the MCP addon enabled on port 9876.")
    except socket.timeout:
        print("Connection timeout. Server may not be responding.")
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    test_connection()
