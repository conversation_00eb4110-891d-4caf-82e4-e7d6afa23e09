#!/usr/bin/env python3
"""
Complex Blender Scene Generator
Creates the most sophisticated 3D scene possible with procedural generation,
advanced materials, lighting, animation, and physics simulation.
"""

import bpy
import bmesh
import mathutils
from mathutils import Vector, Matrix, Euler
import random
import math
import os

def clear_scene():
    """Clear all existing objects from the scene"""
    bpy.ops.object.select_all(action='SELECT')
    bpy.ops.object.delete(use_global=False, confirm=False)
    
    # Clear materials
    for material in bpy.data.materials:
        bpy.data.materials.remove(material)
    
    # Clear textures
    for texture in bpy.data.textures:
        bpy.data.textures.remove(texture)

def create_procedural_city():
    """Create a complex procedural city with buildings, roads, and details"""
    print("Creating procedural city...")
    
    city_size = 40
    block_size = 6
    buildings_created = 0
    
    # Create ground plane
    bpy.ops.mesh.primitive_plane_add(size=city_size*2, location=(0, 0, 0))
    ground = bpy.context.active_object
    ground.name = "CityGround"
    
    # Create road network with intersections
    road_material = bpy.data.materials.new(name="RoadMaterial")
    road_material.use_nodes = True
    road_material.node_tree.nodes.clear()
    
    # Road material nodes
    bsdf = road_material.node_tree.nodes.new(type='ShaderNodeBsdfPrincipled')
    output = road_material.node_tree.nodes.new(type='ShaderNodeOutputMaterial')
    road_material.node_tree.links.new(bsdf.outputs['BSDF'], output.inputs['Surface'])
    bsdf.inputs['Base Color'].default_value = (0.2, 0.2, 0.2, 1.0)
    bsdf.inputs['Roughness'].default_value = 0.8
    
    # Create road grid
    for i in range(-city_size//2, city_size//2, block_size):
        # Horizontal roads
        bpy.ops.mesh.primitive_cube_add(size=1, location=(0, i, 0.05))
        road = bpy.context.active_object
        road.name = f"Road_H_{i}"
        road.scale = (city_size, 1, 0.1)
        road.data.materials.append(road_material)
        
        # Vertical roads
        bpy.ops.mesh.primitive_cube_add(size=1, location=(i, 0, 0.05))
        road = bpy.context.active_object
        road.name = f"Road_V_{i}"
        road.scale = (1, city_size, 0.1)
        road.data.materials.append(road_material)
    
    # Create buildings with variety
    building_materials = create_building_materials()
    
    for block_x in range(-city_size//2, city_size//2, block_size):
        for block_y in range(-city_size//2, city_size//2, block_size):
            if buildings_created >= 60:  # Limit for performance
                break
                
            # Skip road intersections
            if abs(block_x) < 2 or abs(block_y) < 2:
                continue
            
            # Create 1-3 buildings per block
            for _ in range(random.randint(1, 3)):
                if buildings_created >= 60:
                    break
                
                # Random position within block
                x = block_x + random.uniform(-2, 2)
                y = block_y + random.uniform(-2, 2)
                
                # Create building with random architecture
                building_type = random.choice(['skyscraper', 'office', 'residential', 'modern'])
                create_building(x, y, building_type, building_materials)
                buildings_created += 1
    
    print(f"Created {buildings_created} buildings")

def create_building_materials():
    """Create various building materials"""
    materials = {}
    
    # Glass material
    glass_mat = bpy.data.materials.new(name="GlassMaterial")
    glass_mat.use_nodes = True
    glass_mat.node_tree.nodes.clear()
    
    bsdf = glass_mat.node_tree.nodes.new(type='ShaderNodeBsdfPrincipled')
    output = glass_mat.node_tree.nodes.new(type='ShaderNodeOutputMaterial')
    glass_mat.node_tree.links.new(bsdf.outputs['BSDF'], output.inputs['Surface'])
    
    bsdf.inputs['Base Color'].default_value = (0.8, 0.9, 1.0, 1.0)
    bsdf.inputs['Transmission'].default_value = 0.95
    bsdf.inputs['Roughness'].default_value = 0.0
    bsdf.inputs['IOR'].default_value = 1.45
    materials['glass'] = glass_mat
    
    # Concrete material
    concrete_mat = bpy.data.materials.new(name="ConcreteMaterial")
    concrete_mat.use_nodes = True
    concrete_mat.node_tree.nodes.clear()
    
    bsdf = concrete_mat.node_tree.nodes.new(type='ShaderNodeBsdfPrincipled')
    output = concrete_mat.node_tree.nodes.new(type='ShaderNodeOutputMaterial')
    noise = concrete_mat.node_tree.nodes.new(type='ShaderNodeTexNoise')
    
    concrete_mat.node_tree.links.new(bsdf.outputs['BSDF'], output.inputs['Surface'])
    concrete_mat.node_tree.links.new(noise.outputs['Color'], bsdf.inputs['Base Color'])
    
    bsdf.inputs['Roughness'].default_value = 0.9
    noise.inputs['Scale'].default_value = 5.0
    materials['concrete'] = concrete_mat
    
    # Metal material
    metal_mat = bpy.data.materials.new(name="MetalMaterial")
    metal_mat.use_nodes = True
    metal_mat.node_tree.nodes.clear()
    
    bsdf = metal_mat.node_tree.nodes.new(type='ShaderNodeBsdfPrincipled')
    output = metal_mat.node_tree.nodes.new(type='ShaderNodeOutputMaterial')
    metal_mat.node_tree.links.new(bsdf.outputs['BSDF'], output.inputs['Surface'])
    
    bsdf.inputs['Base Color'].default_value = (0.7, 0.7, 0.8, 1.0)
    bsdf.inputs['Metallic'].default_value = 1.0
    bsdf.inputs['Roughness'].default_value = 0.2
    materials['metal'] = metal_mat
    
    return materials

def create_building(x, y, building_type, materials):
    """Create a detailed building at the specified location"""
    if building_type == 'skyscraper':
        # Tall building with glass facade
        height = random.uniform(15, 30)
        width = random.uniform(3, 6)
        depth = random.uniform(3, 6)
        
        bpy.ops.mesh.primitive_cube_add(size=1, location=(x, y, height/2))
        building = bpy.context.active_object
        building.scale = (width, depth, height)
        building.data.materials.append(materials['glass'])
        
        # Add antenna
        bpy.ops.mesh.primitive_cylinder_add(radius=0.1, depth=3, location=(x, y, height + 1.5))
        antenna = bpy.context.active_object
        antenna.data.materials.append(materials['metal'])
        
    elif building_type == 'office':
        # Medium height office building
        height = random.uniform(8, 15)
        width = random.uniform(4, 8)
        depth = random.uniform(4, 8)
        
        bpy.ops.mesh.primitive_cube_add(size=1, location=(x, y, height/2))
        building = bpy.context.active_object
        building.scale = (width, depth, height)
        building.data.materials.append(materials['concrete'])
        
    elif building_type == 'residential':
        # Lower residential building
        height = random.uniform(4, 8)
        width = random.uniform(6, 10)
        depth = random.uniform(6, 10)
        
        bpy.ops.mesh.primitive_cube_add(size=1, location=(x, y, height/2))
        building = bpy.context.active_object
        building.scale = (width, depth, height)
        building.data.materials.append(materials['concrete'])
        
        # Add balconies
        for floor in range(1, int(height//2)):
            balcony_y = y + depth/2 + 0.5
            bpy.ops.mesh.primitive_cube_add(size=1, location=(x, balcony_y, floor * 2))
            balcony = bpy.context.active_object
            balcony.scale = (width * 0.8, 1, 0.2)
            balcony.data.materials.append(materials['concrete'])
    
    elif building_type == 'modern':
        # Modern architectural building with complex geometry
        height = random.uniform(10, 20)
        
        # Create base
        bpy.ops.mesh.primitive_cube_add(size=1, location=(x, y, height/3))
        base = bpy.context.active_object
        base.scale = (6, 6, height/3)
        base.data.materials.append(materials['concrete'])
        
        # Create upper section with twist
        bpy.ops.mesh.primitive_cube_add(size=1, location=(x, y, height*2/3))
        upper = bpy.context.active_object
        upper.scale = (4, 4, height/3)
        upper.rotation_euler.z = math.radians(15)
        upper.data.materials.append(materials['glass'])

def create_advanced_lighting():
    """Set up sophisticated lighting with multiple sources"""
    print("Setting up advanced lighting...")
    
    # Remove default light
    if 'Light' in bpy.data.objects:
        bpy.data.objects.remove(bpy.data.objects['Light'], do_unlink=True)
    
    # Sun light (main directional light)
    bpy.ops.object.light_add(type='SUN', location=(10, 10, 20))
    sun = bpy.context.active_object
    sun.name = "MainSun"
    sun.data.energy = 5.0
    sun.data.color = (1.0, 0.95, 0.8)
    sun.rotation_euler = (math.radians(45), 0, math.radians(45))
    
    # Area lights for ambient lighting
    positions = [(-20, -20, 15), (20, -20, 15), (-20, 20, 15), (20, 20, 15)]
    for i, pos in enumerate(positions):
        bpy.ops.object.light_add(type='AREA', location=pos)
        area_light = bpy.context.active_object
        area_light.name = f"AreaLight_{i}"
        area_light.data.energy = 100.0
        area_light.data.size = 10.0
        area_light.data.color = (0.8, 0.9, 1.0)
        
        # Point towards center
        direction = Vector((0, 0, 0)) - Vector(pos)
        area_light.rotation_euler = direction.to_track_quat('-Z', 'Y').to_euler()
    
    # Street lights
    for i in range(-30, 31, 10):
        for j in range(-30, 31, 10):
            if abs(i) % 6 == 0 or abs(j) % 6 == 0:  # Only on roads
                bpy.ops.object.light_add(type='POINT', location=(i, j, 4))
                street_light = bpy.context.active_object
                street_light.name = f"StreetLight_{i}_{j}"
                street_light.data.energy = 50.0
                street_light.data.color = (1.0, 0.9, 0.7)
                street_light.data.shadow_soft_size = 0.5

def create_particle_systems():
    """Create complex particle systems for atmospheric effects"""
    print("Creating particle systems...")
    
    # Smoke/fog system
    bpy.ops.mesh.primitive_plane_add(size=80, location=(0, 0, 1))
    fog_emitter = bpy.context.active_object
    fog_emitter.name = "FogEmitter"
    
    # Add particle system
    fog_emitter.modifiers.new("ParticleSystem", 'PARTICLE_SYSTEM')
    psys = fog_emitter.particle_systems[0]
    psys.settings.type = 'EMITTER'
    psys.settings.count = 1000
    psys.settings.emit_from = 'FACE'
    psys.settings.physics_type = 'NO'
    psys.settings.particle_size = 2.0
    psys.settings.size_random = 0.5
    psys.settings.lifetime = 250
    psys.settings.normal_factor = 0.1
    
    # Rain system
    bpy.ops.mesh.primitive_plane_add(size=100, location=(0, 0, 50))
    rain_emitter = bpy.context.active_object
    rain_emitter.name = "RainEmitter"
    
    rain_emitter.modifiers.new("ParticleSystem", 'PARTICLE_SYSTEM')
    rain_psys = rain_emitter.particle_systems[0]
    rain_psys.settings.type = 'EMITTER'
    rain_psys.settings.count = 2000
    rain_psys.settings.emit_from = 'FACE'
    rain_psys.settings.physics_type = 'NEWTONIAN'
    rain_psys.settings.particle_size = 0.02
    rain_psys.settings.lifetime = 100
    rain_psys.settings.normal_factor = 0
    rain_psys.settings.factor_random = 0
    
    # Set gravity for rain
    rain_psys.settings.effector_weights.gravity = 2.0

def setup_camera_animation():
    """Create sophisticated camera animation"""
    print("Setting up camera animation...")
    
    # Remove default camera
    if 'Camera' in bpy.data.objects:
        bpy.data.objects.remove(bpy.data.objects['Camera'], do_unlink=True)
    
    # Create new camera
    bpy.ops.object.camera_add(location=(50, -50, 25))
    camera = bpy.context.active_object
    camera.name = "AnimatedCamera"
    
    # Set as active camera
    bpy.context.scene.camera = camera
    
    # Create camera animation path
    keyframes = [
        (1, (50, -50, 25), (math.radians(60), 0, math.radians(45))),
        (50, (0, -60, 30), (math.radians(70), 0, math.radians(90))),
        (100, (-50, -30, 20), (math.radians(65), 0, math.radians(135))),
        (150, (-30, 30, 35), (math.radians(55), 0, math.radians(225))),
        (200, (30, 50, 25), (math.radians(60), 0, math.radians(315))),
        (250, (50, -50, 25), (math.radians(60), 0, math.radians(45))),
    ]
    
    for frame, location, rotation in keyframes:
        bpy.context.scene.frame_set(frame)
        camera.location = location
        camera.rotation_euler = rotation
        camera.keyframe_insert(data_path="location")
        camera.keyframe_insert(data_path="rotation_euler")
    
    # Set animation range
    bpy.context.scene.frame_start = 1
    bpy.context.scene.frame_end = 250

def setup_render_settings():
    """Configure advanced rendering settings"""
    print("Configuring render settings...")
    
    # Set render engine to Cycles for realistic rendering
    bpy.context.scene.render.engine = 'CYCLES'
    
    # Enable GPU rendering if available
    bpy.context.scene.cycles.device = 'GPU'
    
    # Quality settings
    bpy.context.scene.cycles.samples = 128
    bpy.context.scene.render.resolution_x = 1920
    bpy.context.scene.render.resolution_y = 1080
    bpy.context.scene.render.resolution_percentage = 100
    
    # Enable motion blur
    bpy.context.scene.render.motion_blur_shutter = 0.5
    
    # Enable denoising
    bpy.context.scene.cycles.use_denoising = True
    
    # Set output format
    bpy.context.scene.render.image_settings.file_format = 'PNG'
    bpy.context.scene.render.filepath = "//complex_scene_render"

def create_physics_simulation():
    """Add complex physics simulation to the scene"""
    print("Adding physics simulation...")

    # Create falling objects for rigid body simulation
    for i in range(20):
        x = random.uniform(-15, 15)
        y = random.uniform(-15, 15)
        z = random.uniform(30, 50)

        # Create random objects
        obj_type = random.choice(['cube', 'sphere', 'cylinder'])
        if obj_type == 'cube':
            bpy.ops.mesh.primitive_cube_add(location=(x, y, z))
        elif obj_type == 'sphere':
            bpy.ops.mesh.primitive_uv_sphere_add(location=(x, y, z))
        else:
            bpy.ops.mesh.primitive_cylinder_add(location=(x, y, z))

        obj = bpy.context.active_object
        obj.name = f"PhysicsObject_{i}"

        # Add rigid body physics
        bpy.context.view_layer.objects.active = obj
        bpy.ops.rigidbody.object_add()
        obj.rigid_body.type = 'ACTIVE'
        obj.rigid_body.mass = random.uniform(1, 10)
        obj.rigid_body.friction = random.uniform(0.3, 0.8)
        obj.rigid_body.restitution = random.uniform(0.2, 0.7)

    # Add ground as passive rigid body
    if 'CityGround' in bpy.data.objects:
        ground = bpy.data.objects['CityGround']
        bpy.context.view_layer.objects.active = ground
        bpy.ops.rigidbody.object_add()
        ground.rigid_body.type = 'PASSIVE'

def create_procedural_textures():
    """Create advanced procedural textures using shader nodes"""
    print("Creating procedural textures...")

    # Create a complex procedural material for buildings
    proc_mat = bpy.data.materials.new(name="ProceduralBrick")
    proc_mat.use_nodes = True
    nodes = proc_mat.node_tree.nodes
    links = proc_mat.node_tree.links
    nodes.clear()

    # Create node setup for procedural brick texture
    output = nodes.new(type='ShaderNodeOutputMaterial')
    bsdf = nodes.new(type='ShaderNodeBsdfPrincipled')
    brick = nodes.new(type='ShaderNodeTexBrick')
    coord = nodes.new(type='ShaderNodeTexCoord')
    mapping = nodes.new(type='ShaderNodeMapping')
    color_ramp = nodes.new(type='ShaderNodeValToRGB')
    noise = nodes.new(type='ShaderNodeTexNoise')
    mix = nodes.new(type='ShaderNodeMixRGB')

    # Connect nodes
    links.new(coord.outputs['UV'], mapping.inputs['Vector'])
    links.new(mapping.outputs['Vector'], brick.inputs['Vector'])
    links.new(brick.outputs['Color'], mix.inputs['Color1'])
    links.new(noise.outputs['Color'], mix.inputs['Color2'])
    links.new(mix.outputs['Color'], bsdf.inputs['Base Color'])
    links.new(brick.outputs['Fac'], color_ramp.inputs['Fac'])
    links.new(color_ramp.outputs['Color'], bsdf.inputs['Roughness'])
    links.new(bsdf.outputs['BSDF'], output.inputs['Surface'])

    # Configure brick texture
    brick.inputs['Scale'].default_value = 5.0
    brick.inputs['Mortar Size'].default_value = 0.02
    brick.inputs['Color1'].default_value = (0.8, 0.4, 0.2, 1.0)  # Brick color
    brick.inputs['Color2'].default_value = (0.9, 0.9, 0.8, 1.0)  # Mortar color

    # Configure noise for variation
    noise.inputs['Scale'].default_value = 10.0
    mix.inputs['Fac'].default_value = 0.3

    # Apply to some buildings
    for obj in bpy.data.objects:
        if obj.name.startswith('Building') and random.random() > 0.5:
            if obj.data.materials:
                obj.data.materials[0] = proc_mat
            else:
                obj.data.materials.append(proc_mat)

def create_volumetric_atmosphere():
    """Add volumetric atmosphere and fog"""
    print("Creating volumetric atmosphere...")

    # Enable volumetrics in world shader
    world = bpy.context.scene.world
    if not world:
        world = bpy.data.worlds.new("World")
        bpy.context.scene.world = world

    world.use_nodes = True
    nodes = world.node_tree.nodes
    links = world.node_tree.links

    # Clear existing nodes
    nodes.clear()

    # Create world shader setup
    output = nodes.new(type='ShaderNodeOutputWorld')
    background = nodes.new(type='ShaderNodeBackground')
    volume_scatter = nodes.new(type='ShaderNodeVolumeScatter')
    noise = nodes.new(type='ShaderNodeTexNoise')
    coord = nodes.new(type='ShaderNodeTexCoord')

    # Connect nodes
    links.new(background.outputs['Background'], output.inputs['Surface'])
    links.new(volume_scatter.outputs['Volume'], output.inputs['Volume'])
    links.new(coord.outputs['Generated'], noise.inputs['Vector'])
    links.new(noise.outputs['Fac'], volume_scatter.inputs['Density'])

    # Configure atmosphere
    background.inputs['Color'].default_value = (0.05, 0.1, 0.2, 1.0)  # Dark blue sky
    background.inputs['Strength'].default_value = 0.5

    volume_scatter.inputs['Color'].default_value = (0.8, 0.9, 1.0, 1.0)
    volume_scatter.inputs['Density'].default_value = 0.01

    noise.inputs['Scale'].default_value = 0.1
    noise.inputs['Detail'].default_value = 2.0

def create_advanced_geometry():
    """Create complex geometry using modifiers and bmesh operations"""
    print("Creating advanced geometry...")

    # Create a complex architectural structure
    bpy.ops.mesh.primitive_cube_add(location=(0, 0, 40))
    tower = bpy.context.active_object
    tower.name = "ComplexTower"
    tower.scale = (8, 8, 20)

    # Add array modifier for repetition
    array_mod = tower.modifiers.new("Array", 'ARRAY')
    array_mod.count = 3
    array_mod.relative_offset_displace[2] = 0.8
    array_mod.use_relative_offset = True

    # Add screw modifier for spiral effect
    screw_mod = tower.modifiers.new("Screw", 'SCREW')
    screw_mod.angle = math.radians(45)
    screw_mod.screw_offset = 5
    screw_mod.iterations = 1
    screw_mod.axis = 'Z'

    # Add subdivision surface for smoothness
    subsurf_mod = tower.modifiers.new("Subdivision", 'SUBSURF')
    subsurf_mod.levels = 2

    # Create organic landscape using displacement
    bpy.ops.mesh.primitive_plane_add(size=100, location=(0, 0, -5))
    landscape = bpy.context.active_object
    landscape.name = "OrganicLandscape"

    # Add subdivision for detail
    bpy.context.view_layer.objects.active = landscape
    bpy.ops.object.mode_set(mode='EDIT')
    bpy.ops.mesh.subdivide(number_cuts=10)
    bpy.ops.object.mode_set(mode='OBJECT')

    # Add displacement modifier
    displace_mod = landscape.modifiers.new("Displace", 'DISPLACE')

    # Create displacement texture
    disp_tex = bpy.data.textures.new("DisplacementTexture", 'CLOUDS')
    disp_tex.noise_scale = 2.0
    disp_tex.noise_depth = 4
    displace_mod.texture = disp_tex
    displace_mod.strength = 3.0

def create_complex_scene():
    """Main function to create the most complex scene possible"""
    print("Starting ultra-complex scene creation...")

    # Clear existing scene
    clear_scene()

    # Create all components in order
    create_procedural_city()
    create_advanced_lighting()
    create_particle_systems()
    create_physics_simulation()
    create_procedural_textures()
    create_volumetric_atmosphere()
    create_advanced_geometry()
    setup_camera_animation()
    setup_render_settings()

    print("Ultra-complex scene creation completed!")
    print("Scene includes:")
    print("- Procedural city with 60+ buildings")
    print("- Advanced materials (glass, concrete, metal, procedural brick)")
    print("- Sophisticated lighting setup with multiple light types")
    print("- Particle systems (fog and rain)")
    print("- Physics simulation with 20 falling objects")
    print("- Procedural textures and shader nodes")
    print("- Volumetric atmosphere and fog")
    print("- Complex geometry with modifiers")
    print("- Organic landscape with displacement")
    print("- Animated camera with 250-frame sequence")
    print("- Optimized render settings for Cycles")

    return "Ultra-complex scene successfully created with all advanced features!"

if __name__ == "__main__":
    create_complex_scene()
